#!/usr/bin/env python3
"""
开发环境启动脚本
"""

import os
import sys

# 设置环境变量
os.environ['ENVIRONMENT'] = 'development'

# 导入并启动应用
if __name__ == '__main__':
    from sql_tools import app, HOST, PORT, ENVIRONMENT, DEFAULT_DATABASE, API_TOKEN

    print("=" * 50)
    print("SQL执行服务 - 开发模式")
    print("=" * 50)
    print(f"环境: {ENVIRONMENT}")
    print(f"地址: http://{HOST}:{PORT}")
    print(f"API Token: {API_TOKEN}")
    print(f"默认数据库: {DEFAULT_DATABASE}")
    print("使用模拟数据，无需真实数据库连接")
    print("支持任意数据库名称")
    print("=" * 50)

    app.run(host=HOST, port=PORT, debug=True)
