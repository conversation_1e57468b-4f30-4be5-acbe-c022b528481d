#sql_tools_demo.py - 演示版本（使用模拟数据库）
 
from flask import Flask, request, jsonify
import jwt
import re
from functools import wraps
from datetime import datetime, timedelta, timezone

# JWT密钥配置
JWT_SECRET_KEY = 'your-secret-key-change-this-in-production'
JWT_ALGORITHM = 'HS256'

# 模拟数据库配置
DEMO_DATABASES = {
    'analysis_gz': {
        'users': [
            ['id', 'name', 'email'],
            [1, '张三', '<EMAIL>'],
            [2, '李四', '<EMAIL>'],
            [3, '王五', '<EMAIL>']
        ],
        'orders': [
            ['order_id', 'user_id', 'amount'],
            [1001, 1, 299.99],
            [1002, 2, 199.50],
            [1003, 1, 399.00]
        ]
    },
    'test_db': {
        'products': [
            ['product_id', 'name', 'price'],
            [1, '笔记本电脑', 5999.00],
            [2, '手机', 2999.00]
        ]
    }
}

# 允许的数据库列表
ALLOWED_DATABASES = list(DEMO_DATABASES.keys())

# 初始化Flask应用
app = Flask(__name__)

# 生成JWT token
def generate_token(username):
    payload = {
        'username': username,
        'exp': datetime.now(timezone.utc) + timedelta(hours=24)  # 24小时过期
    }
    return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

# 验证JWT token
def verify_token(token):
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload['username']
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

# 鉴权装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
        
        if token.startswith('Bearer '):
            token = token[7:]
        
        username = verify_token(token)
        if not username:
            return jsonify({'error': 'Token is invalid or expired'}), 401
        
        return f(*args, **kwargs)
    return decorated

# 验证SQL是否只包含SELECT语句
def is_select_only(sql):
    # 移除注释和多余空格
    sql_clean = re.sub(r'--.*$', '', sql, flags=re.MULTILINE)
    sql_clean = re.sub(r'/\*.*?\*/', '', sql_clean, flags=re.DOTALL)
    sql_clean = sql_clean.strip()
    
    # 检查是否只包含SELECT语句
    # 允许WITH子句开头的CTE查询
    pattern = r'^\s*(WITH\s+.*?\s+)?SELECT\s+.*$'
    return bool(re.match(pattern, sql_clean, re.IGNORECASE | re.DOTALL))

# 模拟SQL执行
def execute_demo_query(database, sql):
    """模拟执行SQL查询"""
    if database not in DEMO_DATABASES:
        return None
    
    db_data = DEMO_DATABASES[database]
    
    # 简单的SQL解析（仅用于演示）
    sql_lower = sql.lower().strip()
    
    if 'select *' in sql_lower:
        # 查找表名
        for table_name in db_data.keys():
            if table_name in sql_lower:
                return db_data[table_name]
    
    if 'select count(*)' in sql_lower:
        # 查找表名并返回计数
        for table_name in db_data.keys():
            if table_name in sql_lower:
                return [['count(*)'], [len(db_data[table_name]) - 1]]  # 减去表头
    
    if 'select database()' in sql_lower:
        return [['database()'], [database]]
    
    if sql_lower == 'select 1 as test_column':
        return [['test_column'], [1]]
    
    if sql_lower == 'select 1':
        return [['1'], [1]]
    
    # 默认返回示例数据
    return [['demo_column'], ['demo_value']]

# 简单的用户验证（实际项目中应该使用数据库存储用户信息）
USERS = {
    'admin': 'password123',
    'user1': 'mypassword',
    'guest': 'guestpass'
}

# HTTP接口：用户登录
@app.route('/login', methods=['POST'])
def login():
    data = request.json
    if not data or 'username' not in data or 'password' not in data:
        return jsonify({"error": "Username and password are required"}), 400
    
    username = data['username']
    password = data['password']
    
    if username in USERS and USERS[username] == password:
        token = generate_token(username)
        return jsonify({"token": token, "message": "Login successful"})
    else:
        return jsonify({"error": "Invalid username or password"}), 401

# HTTP接口：执行SQL
@app.route('/execute', methods=['POST'])
@token_required
def execute_sql():
    # 获取请求中的SQL语句和数据库
    data = request.json
    if not data or 'sql' not in data:
        return jsonify({"error": "SQL statement is required"}), 400
 
    sql = data['sql']
    database = data.get('database', 'analysis_gz')  # 默认数据库
    
    # 验证SQL是否只包含SELECT语句
    if not is_select_only(sql):
        return jsonify({"error": "Only SELECT statements are allowed"}), 403
    
    # 验证数据库是否允许
    if database not in ALLOWED_DATABASES:
        return jsonify({"error": f"Database '{database}' is not allowed"}), 400
    
    # 执行模拟SQL
    result = execute_demo_query(database, sql)
    
    if result is None:
        return jsonify({"error": "Failed to execute SQL"}), 500
 
    # 返回结果
    return jsonify({
        "result": result,
        "database": database,
        "row_count": len(result) - 1 if len(result) > 1 else 0,
        "note": "This is a demo version with simulated data"
    })

# HTTP接口：获取允许的数据库列表
@app.route('/databases', methods=['GET'])
@token_required
def get_databases():
    return jsonify({"databases": ALLOWED_DATABASES})

# HTTP接口：获取数据库表信息
@app.route('/tables', methods=['GET'])
@token_required
def get_tables():
    database = request.args.get('database', 'analysis_gz')
    if database not in DEMO_DATABASES:
        return jsonify({"error": f"Database '{database}' not found"}), 404
    
    tables = list(DEMO_DATABASES[database].keys())
    return jsonify({"database": database, "tables": tables})

# HTTP接口：健康检查
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy", "message": "SQL execution demo service is running"})
 
# 启动Flask应用
if __name__ == '__main__':
    print("启动SQL执行演示服务...")
    print("可用的演示数据库:", ALLOWED_DATABASES)
    print("可用的用户账号:", list(USERS.keys()))
    app.run(host='0.0.0.0', port=3001)
