#sql_tools.py

from flask import Flask, request, jsonify
import mysql.connector
import re
import os
from functools import wraps
from dotenv import load_dotenv

# 加载环境配置
def load_environment():
    """根据ENVIRONMENT变量加载对应的环境配置"""
    env = os.getenv('ENVIRONMENT', 'development')

    if env == 'production':
        load_dotenv('.env.production')
    elif env == 'development':
        load_dotenv('.env.development')
    else:
        load_dotenv('.env')

    # 再次加载，确保环境变量被正确设置
    load_dotenv(override=True)

# 初始化环境配置
load_environment()

# 获取配置
API_TOKEN = os.getenv('API_TOKEN', 'default-token-change-this')
ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')
HOST = os.getenv('HOST', '0.0.0.0')
PORT = int(os.getenv('PORT', 3000))

# 数据库配置
DB_CONFIG = {
    'user': os.getenv('DB_USER', ''),
    'password': os.getenv('DB_PASSWORD', ''),
    'host': os.getenv('DB_HOST', ''),
    'database': os.getenv('DB_DATABASE', ''),
    'raise_on_warnings': True
} if ENVIRONMENT == 'production' else None

# 允许的数据库列表
ALLOWED_DATABASES = os.getenv('ALLOWED_DATABASES', 'analysis_gz,test_db').split(',')

# 开发环境模拟数据
DEMO_DATABASES = {
    'analysis_gz': {
        'users': [
            ['id', 'name', 'email'],
            [1, '张三', '<EMAIL>'],
            [2, '李四', '<EMAIL>'],
            [3, '王五', '<EMAIL>']
        ],
        'orders': [
            ['order_id', 'user_id', 'amount'],
            [1001, 1, 299.99],
            [1002, 2, 199.50],
            [1003, 1, 399.00]
        ]
    },
    'test_db': {
        'products': [
            ['product_id', 'name', 'price'],
            [1, '笔记本电脑', 5999.00],
            [2, '手机', 2999.00]
        ]
    }
}

# 初始化Flask应用
app = Flask(__name__)

# 简化的鉴权装饰器 - 使用固定token
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401

        if token.startswith('Bearer '):
            token = token[7:]

        if token != API_TOKEN:
            return jsonify({'error': 'Invalid token'}), 401

        return f(*args, **kwargs)
    return decorated

# 验证SQL是否只包含SELECT语句
def is_select_only(sql):
    # 移除注释和多余空格
    sql_clean = re.sub(r'--.*$', '', sql, flags=re.MULTILINE)
    sql_clean = re.sub(r'/\*.*?\*/', '', sql_clean, flags=re.DOTALL)
    sql_clean = sql_clean.strip()

    # 检查是否只包含SELECT语句
    # 允许WITH子句开头的CTE查询
    pattern = r'^\s*(WITH\s+.*?\s+)?SELECT\s+.*$'
    return bool(re.match(pattern, sql_clean, re.IGNORECASE | re.DOTALL))
 
# 连接数据库 (仅生产环境)
def connect_to_database(database=None):
    if ENVIRONMENT != 'production':
        return None  # 开发环境不使用真实数据库

    try:
        db_config = DB_CONFIG.copy()
        if database:
            if database not in ALLOWED_DATABASES:
                raise ValueError(f"Database '{database}' is not allowed")
            db_config['database'] = database

        conn = mysql.connector.connect(**db_config)
        print(f"Connected to MySQL database: {db_config['database']}")
        return conn
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        return None
    except ValueError as err:
        print(f"Error: {err}")
        return None

# 模拟SQL执行 (开发环境)
def execute_demo_query(database, sql):
    """模拟执行SQL查询"""
    if database not in DEMO_DATABASES:
        return None

    db_data = DEMO_DATABASES[database]

    # 简单的SQL解析（仅用于演示）
    sql_lower = sql.lower().strip()

    if 'select *' in sql_lower:
        # 查找表名
        for table_name in db_data.keys():
            if table_name in sql_lower:
                return db_data[table_name]

    if 'select count(*)' in sql_lower:
        # 查找表名并返回计数
        for table_name in db_data.keys():
            if table_name in sql_lower:
                return [['count(*)'], [len(db_data[table_name]) - 1]]  # 减去表头

    if 'select database()' in sql_lower:
        return [['database()'], [database]]

    if sql_lower == 'select 1 as test_column':
        return [['test_column'], [1]]

    if sql_lower == 'select 1':
        return [['1'], [1]]

    # 默认返回示例数据
    return [['demo_column'], ['demo_value']]
 
# 执行SQL查询
def execute_query(conn, sql):
    cursor = conn.cursor()
    try:
        cursor.execute(sql)
        if sql.strip().lower().startswith("select"):
            # 如果是查询操作，返回结果
            result = cursor.fetchall()
            return result
        else:
            # 如果是插入、更新、删除操作，提交事务并返回受影响的行数
            conn.commit()
            return cursor.rowcount
    except mysql.connector.Error as err:
        print(f"Error executing SQL: {err}")
        return None
    finally:
        cursor.close()
 
# HTTP接口：执行SQL
@app.route('/execute', methods=['POST'])
@token_required
def execute_sql():
    # 获取请求中的SQL语句和数据库
    data = request.json
    if not data or 'sql' not in data:
        return jsonify({"error": "SQL statement is required"}), 400

    sql = data['sql']
    database = data.get('database', ALLOWED_DATABASES[0])  # 默认使用第一个允许的数据库

    # 验证SQL是否只包含SELECT语句
    if not is_select_only(sql):
        return jsonify({"error": "Only SELECT statements are allowed"}), 403

    # 验证数据库是否允许
    if database not in ALLOWED_DATABASES:
        return jsonify({"error": f"Database '{database}' is not allowed"}), 400

    if ENVIRONMENT == 'production':
        # 生产环境：连接真实数据库
        conn = connect_to_database(database)
        if not conn:
            error_msg = f"Failed to connect to database"
            if database:
                error_msg += f" '{database}'"
            return jsonify({"error": error_msg}), 500

        # 执行SQL
        result = execute_query(conn, sql)
        conn.close()

        if result is None:
            return jsonify({"error": "Failed to execute SQL"}), 500
    else:
        # 开发环境：使用模拟数据
        result = execute_demo_query(database, sql)
        if result is None:
            return jsonify({"error": "Failed to execute SQL in demo mode"}), 500

    # 返回结果
    response_data = {
        "result": result,
        "database": database,
        "row_count": len(result) - 1 if len(result) > 1 else 0,
        "environment": ENVIRONMENT
    }

    if ENVIRONMENT == 'development':
        response_data["note"] = "This is development mode with simulated data"

    return jsonify(response_data)

# HTTP接口：获取允许的数据库列表
@app.route('/databases', methods=['GET'])
@token_required
def get_databases():
    return jsonify({"databases": ALLOWED_DATABASES})

# HTTP接口：获取数据库表信息
@app.route('/tables', methods=['GET'])
@token_required
def get_tables():
    database = request.args.get('database', ALLOWED_DATABASES[0])

    if database not in ALLOWED_DATABASES:
        return jsonify({"error": f"Database '{database}' not found"}), 404

    if ENVIRONMENT == 'development':
        # 开发环境：返回模拟表信息
        if database in DEMO_DATABASES:
            tables = list(DEMO_DATABASES[database].keys())
            return jsonify({"database": database, "tables": tables})
        else:
            return jsonify({"database": database, "tables": []})
    else:
        # 生产环境：查询真实数据库表信息
        conn = connect_to_database(database)
        if not conn:
            return jsonify({"error": f"Failed to connect to database '{database}'"}), 500

        try:
            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            cursor.close()
            conn.close()
            return jsonify({"database": database, "tables": tables})
        except mysql.connector.Error as err:
            conn.close()
            return jsonify({"error": f"Failed to get tables: {err}"}), 500

# HTTP接口：健康检查
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        "status": "healthy",
        "message": f"SQL execution service is running in {ENVIRONMENT} mode",
        "environment": ENVIRONMENT,
        "allowed_databases": ALLOWED_DATABASES
    })

# HTTP接口：获取配置信息
@app.route('/config', methods=['GET'])
@token_required
def get_config():
    return jsonify({
        "environment": ENVIRONMENT,
        "allowed_databases": ALLOWED_DATABASES,
        "host": HOST,
        "port": PORT
    })

# 启动Flask应用
if __name__ == '__main__':
    print(f"启动SQL执行服务...")
    print(f"环境: {ENVIRONMENT}")
    print(f"端口: {PORT}")
    print(f"允许的数据库: {ALLOWED_DATABASES}")
    print(f"API Token: {API_TOKEN}")

    if ENVIRONMENT == 'development':
        print("开发模式：使用模拟数据")
    else:
        print("生产模式：连接真实数据库")

    app.run(host=HOST, port=PORT)