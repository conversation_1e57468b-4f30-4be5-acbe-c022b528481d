#sql_tools.py

from flask import Flask, request, jsonify
import mysql.connector
import re
import os
from functools import wraps
from dotenv import load_dotenv

# 加载环境配置
def load_environment():
    """根据ENVIRONMENT变量加载对应的环境配置"""
    env = os.getenv('ENVIRONMENT', 'development')

    if env == 'production':
        load_dotenv('.env.production')
    elif env == 'development':
        load_dotenv('.env.development')
    else:
        load_dotenv('.env')

    # 再次加载，确保环境变量被正确设置
    load_dotenv(override=True)

# 初始化环境配置
load_environment()

# 获取配置
API_TOKEN = os.getenv('API_TOKEN', 'default-token-change-this')
ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')
HOST = os.getenv('HOST', '0.0.0.0')
PORT = int(os.getenv('PORT', 3000))

# 数据库配置
DB_CONFIG = {
    'user': os.getenv('DB_USER', ''),
    'password': os.getenv('DB_PASSWORD', ''),
    'host': os.getenv('DB_HOST', ''),
    'port': int(os.getenv('DB_PORT', 3306)),
    'database': os.getenv('DB_DATABASE', ''),
    'raise_on_warnings': True
}

# 检查是否有有效的数据库配置
HAS_DB_CONFIG = all([
    os.getenv('DB_USER'),
    os.getenv('DB_PASSWORD'),
    os.getenv('DB_HOST'),
    os.getenv('DB_DATABASE')
])

# 默认数据库
DEFAULT_DATABASE = os.getenv('DB_DATABASE', 'analysis_gz')



# 初始化Flask应用
app = Flask(__name__)

# 简化的鉴权装饰器 - 使用固定token
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401

        if token.startswith('Bearer '):
            token = token[7:]

        if token != API_TOKEN:
            return jsonify({'error': 'Invalid token'}), 401

        return f(*args, **kwargs)
    return decorated

# 验证SQL是否只包含SELECT语句
def is_select_only(sql):
    # 移除注释和多余空格
    sql_clean = re.sub(r'--.*$', '', sql, flags=re.MULTILINE)
    sql_clean = re.sub(r'/\*.*?\*/', '', sql_clean, flags=re.DOTALL)
    sql_clean = sql_clean.strip()

    # 检查是否只包含SELECT语句
    # 允许WITH子句开头的CTE查询
    pattern = r'^\s*(WITH\s+.*?\s+)?SELECT\s+.*$'
    return bool(re.match(pattern, sql_clean, re.IGNORECASE | re.DOTALL))
 
# 连接数据库
def connect_to_database(database=None):
    if not HAS_DB_CONFIG:
        return None  # 没有数据库配置时返回None

    try:
        db_config = DB_CONFIG.copy()
        if database:
            db_config['database'] = database

        conn = mysql.connector.connect(**db_config)
        print(f"Connected to MySQL database: {db_config['database']}")
        return conn
    except mysql.connector.Error as err:
        print(f"Database connection error: {err}")
        return None


 
# 执行SQL查询
def execute_query(conn, sql):
    cursor = conn.cursor()
    try:
        cursor.execute(sql)
        if sql.strip().lower().startswith("select"):
            # 如果是查询操作，返回结果
            result = cursor.fetchall()
            return result
        else:
            # 如果是插入、更新、删除操作，提交事务并返回受影响的行数
            conn.commit()
            return cursor.rowcount
    except mysql.connector.Error as err:
        print(f"Error executing SQL: {err}")
        return None
    finally:
        cursor.close()
 
# HTTP接口：执行SQL
@app.route('/execute', methods=['POST'])
@token_required
def execute_sql():
    # 获取请求中的SQL语句和数据库
    data = request.json
    if not data or 'sql' not in data:
        return jsonify({"error": "SQL statement is required"}), 400

    sql = data['sql']
    database = data.get('database', DEFAULT_DATABASE)  # 默认使用配置的数据库

    # 验证SQL是否只包含SELECT语句
    if not is_select_only(sql):
        return jsonify({"error": "Only SELECT statements are allowed"}), 403

    # 连接数据库
    conn = connect_to_database(database)

    if not conn:
        return jsonify({"error": "Failed to connect to database"}), 500

    # 执行SQL
    try:
        result = execute_query(conn, sql)
        conn.close()

        if result is None:
            return jsonify({"error": "Failed to execute SQL"}), 500

    except Exception as e:
        conn.close()
        return jsonify({"error": f"Database execution error: {str(e)}"}), 500

    # 返回结果
    return jsonify({
        "result": result,
        "database": database,
        "row_count": len(result) - 1 if len(result) > 1 else 0,
        "environment": ENVIRONMENT
    })

# HTTP接口：获取数据库表信息
@app.route('/tables', methods=['GET'])
@token_required
def get_tables():
    database = request.args.get('database', DEFAULT_DATABASE)

    # 连接数据库
    conn = connect_to_database(database)

    if not conn:
        return jsonify({"error": f"Failed to connect to database '{database}'"}), 500

    # 查询数据库表信息
    try:
        cursor = conn.cursor()
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        cursor.close()
        conn.close()
        return jsonify({
            "database": database,
            "tables": tables
        })
    except mysql.connector.Error as err:
        conn.close()
        return jsonify({"error": f"Failed to get tables: {err}"}), 500

# HTTP接口：健康检查
@app.route('/health', methods=['GET'])
def health_check():
    # 检查数据库连接状态
    db_status = "not_configured"
    if HAS_DB_CONFIG:
        test_conn = connect_to_database()
        if test_conn:
            test_conn.close()
            db_status = "connected"
        else:
            db_status = "connection_failed"

    return jsonify({
        "status": "healthy",
        "message": f"SQL execution service is running in {ENVIRONMENT} mode",
        "environment": ENVIRONMENT,
        "default_database": DEFAULT_DATABASE,
        "database_status": db_status
    })

# HTTP接口：获取配置信息
@app.route('/config', methods=['GET'])
@token_required
def get_config():
    return jsonify({
        "environment": ENVIRONMENT,
        "default_database": DEFAULT_DATABASE,
        "host": HOST,
        "port": PORT,
        "database_configured": HAS_DB_CONFIG
    })

# 启动Flask应用
if __name__ == '__main__':
    print(f"启动SQL执行服务...")
    print(f"环境: {ENVIRONMENT}")
    print(f"端口: {PORT}")
    print(f"默认数据库: {DEFAULT_DATABASE}")
    print(f"API Token: {API_TOKEN}")

    if HAS_DB_CONFIG:
        print("数据库配置: 已配置")
        # 测试数据库连接
        test_conn = connect_to_database()
        if test_conn:
            test_conn.close()
            print("数据库连接: 成功")
        else:
            print("数据库连接: 失败")
    else:
        print("数据库配置: 未配置")

    # 生产环境警告
    if ENVIRONMENT == 'production':
        print("\n" + "="*60)
        print("⚠️  警告: 当前使用Flask开发服务器")
        print("⚠️  生产环境建议使用: python start_prod_gunicorn.py")
        print("⚠️  或手动运行: gunicorn --bind 0.0.0.0:3000 sql_tools:app")
        print("="*60 + "\n")

    # 开发环境使用debug模式
    debug_mode = ENVIRONMENT == 'development'
    app.run(host=HOST, port=PORT, debug=debug_mode)