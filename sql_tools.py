#sql_tools.py

from flask import Flask, request, jsonify
import mysql.connector
import jwt
import re
from functools import wraps
from datetime import datetime, timedelta, timezone
 
# JWT密钥配置
JWT_SECRET_KEY = 'your-secret-key-change-this-in-production'
JWT_ALGORITHM = 'HS256'

# 数据库连接配置
config = {
    'user': 'root',
    'password': 'ncms@Abb.235',
    'host': '**********:4000',
    'database': 'analysis_gz',
    'raise_on_warnings': True
}

# 允许的数据库列表
ALLOWED_DATABASES = ['analysis_gz', 'test_db', 'reporting_db']
 
# 初始化Flask应用
app = Flask(__name__)

# 生成JWT token
def generate_token(username):
    payload = {
        'username': username,
        'exp': datetime.now(timezone.utc) + timedelta(hours=24)  # 24小时过期
    }
    return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

# 验证JWT token
def verify_token(token):
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload['username']
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

# 鉴权装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401

        if token.startswith('Bearer '):
            token = token[7:]

        username = verify_token(token)
        if not username:
            return jsonify({'error': 'Token is invalid or expired'}), 401

        return f(*args, **kwargs)
    return decorated

# 验证SQL是否只包含SELECT语句
def is_select_only(sql):
    # 移除注释和多余空格
    sql_clean = re.sub(r'--.*$', '', sql, flags=re.MULTILINE)
    sql_clean = re.sub(r'/\*.*?\*/', '', sql_clean, flags=re.DOTALL)
    sql_clean = sql_clean.strip()

    # 检查是否只包含SELECT语句
    # 允许WITH子句开头的CTE查询
    pattern = r'^\s*(WITH\s+.*?\s+)?SELECT\s+.*$'
    return bool(re.match(pattern, sql_clean, re.IGNORECASE | re.DOTALL))
 
# 连接数据库
def connect_to_database(database=None):
    try:
        db_config = config.copy()
        if database:
            if database not in ALLOWED_DATABASES:
                raise ValueError(f"Database '{database}' is not allowed")
            db_config['database'] = database

        conn = mysql.connector.connect(**db_config)
        print(f"Connected to MySQL database: {db_config['database']}")
        return conn
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        return None
    except ValueError as err:
        print(f"Error: {err}")
        return None
 
# 执行SQL查询
def execute_query(conn, sql):
    cursor = conn.cursor()
    try:
        cursor.execute(sql)
        if sql.strip().lower().startswith("select"):
            # 如果是查询操作，返回结果
            result = cursor.fetchall()
            return result
        else:
            # 如果是插入、更新、删除操作，提交事务并返回受影响的行数
            conn.commit()
            return cursor.rowcount
    except mysql.connector.Error as err:
        print(f"Error executing SQL: {err}")
        return None
    finally:
        cursor.close()
 
# 简单的用户验证（实际项目中应该使用数据库存储用户信息）
USERS = {
    'admin': 'password123',
    'user1': 'mypassword',
    'guest': 'guestpass'
}

# HTTP接口：用户登录
@app.route('/login', methods=['POST'])
def login():
    data = request.json
    if not data or 'username' not in data or 'password' not in data:
        return jsonify({"error": "Username and password are required"}), 400

    username = data['username']
    password = data['password']

    if username in USERS and USERS[username] == password:
        token = generate_token(username)
        return jsonify({"token": token, "message": "Login successful"})
    else:
        return jsonify({"error": "Invalid username or password"}), 401

# HTTP接口：执行SQL
@app.route('/execute', methods=['POST'])
@token_required
def execute_sql():
    # 获取请求中的SQL语句和数据库
    data = request.json
    if not data or 'sql' not in data:
        return jsonify({"error": "SQL statement is required"}), 400

    sql = data['sql']
    database = data.get('database')  # 可选的数据库参数

    # 验证SQL是否只包含SELECT语句
    if not is_select_only(sql):
        return jsonify({"error": "Only SELECT statements are allowed"}), 403

    # 连接到指定数据库
    conn = connect_to_database(database)
    if not conn:
        error_msg = f"Failed to connect to database"
        if database:
            error_msg += f" '{database}'"
        return jsonify({"error": error_msg}), 500

    # 执行SQL
    result = execute_query(conn, sql)
    conn.close()

    if result is None:
        return jsonify({"error": "Failed to execute SQL"}), 500

    # 返回结果
    return jsonify({
        "result": result,
        "database": database or config['database'],
        "row_count": len(result) if isinstance(result, list) else 0
    })

# HTTP接口：获取允许的数据库列表
@app.route('/databases', methods=['GET'])
@token_required
def get_databases():
    return jsonify({"databases": ALLOWED_DATABASES})

# HTTP接口：健康检查
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy", "message": "SQL execution service is running"})

# 启动Flask应用
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000)