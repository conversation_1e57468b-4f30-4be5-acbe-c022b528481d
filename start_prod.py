#!/usr/bin/env python3
"""
生产环境启动脚本
"""

import os
import sys

# 设置环境变量
os.environ['ENVIRONMENT'] = 'production'

# 导入并启动应用
if __name__ == '__main__':
    from sql_tools import app, HOST, PORT, ENVIRONMENT, ALLOWED_DATABASES, API_TOKEN
    
    print("=" * 50)
    print("SQL执行服务 - 生产模式")
    print("=" * 50)
    print(f"环境: {ENVIRONMENT}")
    print(f"地址: http://{HOST}:{PORT}")
    print(f"API Token: {API_TOKEN}")
    print(f"允许的数据库: {ALLOWED_DATABASES}")
    print("连接真实数据库")
    print("=" * 50)
    print("警告: 这是生产环境，请确保:")
    print("1. 已正确配置数据库连接")
    print("2. 已修改默认的API Token")
    print("3. 网络安全配置已就绪")
    print("=" * 50)
    
    app.run(host=HOST, port=PORT)
