# SQL执行服务

这是一个安全的SQL执行服务，支持鉴权、只允许SELECT查询，并可以指定数据库。

## 功能特性

1. **JWT鉴权** - 所有SQL执行请求都需要有效的JWT token
2. **只允许SELECT** - 只能执行SELECT查询，禁止INSERT/UPDATE/DELETE等操作
3. **多数据库支持** - 可以在请求时指定要查询的数据库
4. **安全验证** - 验证数据库是否在允许列表中

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

在 `sql_tools.py` 中修改以下配置：

1. **JWT密钥**: 修改 `JWT_SECRET_KEY`
2. **数据库配置**: 修改 `config` 字典
3. **允许的数据库**: 修改 `ALLOWED_DATABASES` 列表
4. **用户账号**: 修改 `USERS` 字典（生产环境建议使用数据库存储）

## API接口

### 1. 用户登录
```
POST /login
Content-Type: application/json

{
    "username": "admin",
    "password": "password123"
}
```

响应：
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "message": "Login successful"
}
```

### 2. 执行SQL查询
```
POST /execute
Authorization: Bearer <token>
Content-Type: application/json

{
    "sql": "SELECT * FROM users LIMIT 10",
    "database": "analysis_gz"  // 可选，不指定则使用默认数据库
}
```

响应：
```json
{
    "result": [
        ["id", "name", "email"],
        [1, "张三", "<EMAIL>"],
        [2, "李四", "<EMAIL>"]
    ],
    "database": "analysis_gz",
    "row_count": 2
}
```

### 3. 获取允许的数据库列表
```
GET /databases
Authorization: Bearer <token>
```

响应：
```json
{
    "databases": ["analysis_gz", "test_db", "reporting_db"]
}
```

### 4. 健康检查
```
GET /health
```

响应：
```json
{
    "status": "healthy",
    "message": "SQL execution service is running"
}
```

## 启动服务

```bash
python sql_tools.py
```

服务将在 `http://0.0.0.0:3000` 启动。

## 安全说明

1. **JWT Token**: 默认24小时过期
2. **SQL限制**: 只允许SELECT语句，支持WITH子句的CTE查询
3. **数据库白名单**: 只能访问预定义的数据库列表
4. **密码存储**: 当前使用明文存储，生产环境建议使用哈希加密

## 使用示例

```bash
# 1. 登录获取token
curl -X POST http://localhost:3000/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password123"}'

# 2. 使用token执行查询
curl -X POST http://localhost:3000/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-token>" \
  -d '{"sql": "SELECT COUNT(*) FROM users", "database": "analysis_gz"}'
```
