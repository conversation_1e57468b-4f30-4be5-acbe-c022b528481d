# SQL执行服务

这是一个安全的SQL执行服务，支持固定Token鉴权、只允许SELECT查询，并可以指定数据库。支持开发和生产环境配置。

## 功能特性

1. **固定Token鉴权** - 使用预设的API Token进行鉴权，无需登录
2. **只允许SELECT** - 只能执行SELECT查询，禁止INSERT/UPDATE/DELETE等操作
3. **多数据库支持** - 可以在请求时指定任意数据库名称
4. **环境配置管理** - 支持开发和生产环境的独立配置
5. **灵活性** - 无数据库白名单限制，支持动态数据库访问

## 安装依赖

```bash
pip install -r requirements.txt
```

## 环境配置

项目使用 `.env` 文件管理不同环境的配置：

- `.env` - 默认配置
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

### 配置项说明

```bash
# 环境类型
ENVIRONMENT=development  # development 或 production

# 服务配置
HOST=0.0.0.0
PORT=3000

# API Token (请务必修改)
API_TOKEN=your-api-token

# 数据库配置 (仅生产环境需要)
DB_USER=root
DB_PASSWORD=your-password
DB_HOST=your-host
DB_DATABASE=your-default-database
```

## 启动服务

### 开发环境
```bash
python start_dev.py
# 或者
ENVIRONMENT=development python sql_tools.py
```

### 生产环境
```bash
python start_prod.py
# 或者
ENVIRONMENT=production python sql_tools.py
```

## API接口

### 1. 执行SQL查询
```
POST /execute
Authorization: Bearer <your-api-token>
Content-Type: application/json

{
    "sql": "SELECT * FROM users LIMIT 10",
    "database": "analysis_gz"  // 可选，不指定则使用第一个允许的数据库
}
```

响应：
```json
{
    "result": [
        ["id", "name", "email"],
        [1, "张三", "<EMAIL>"],
        [2, "李四", "<EMAIL>"]
    ],
    "database": "analysis_gz",
    "row_count": 2,
    "environment": "development"
}
```

### 2. 获取数据库表信息
```
GET /tables?database=analysis_gz
Authorization: Bearer <your-api-token>
```

响应：
```json
{
    "database": "analysis_gz",
    "tables": ["users", "orders"]
}
```

### 3. 获取配置信息
```
GET /config
Authorization: Bearer <your-api-token>
```

响应：
```json
{
    "environment": "development",
    "default_database": "analysis_gz",
    "host": "0.0.0.0",
    "port": 3001,
    "database_configured": true
}
```

### 4. 健康检查
```
GET /health
```

响应：
```json
{
    "status": "healthy",
    "message": "SQL execution service is running in development mode",
    "environment": "development",
    "default_database": "analysis_gz",
    "database_status": "connected"
}
```

## 安全说明

1. **固定Token**: 使用预设的API Token，请务必修改默认值
2. **SQL限制**: 只允许SELECT语句，支持WITH子句的CTE查询
3. **数据库访问**: 支持任意数据库名称，请确保数据库访问权限正确配置
4. **环境隔离**: 开发和生产环境都连接真实数据库，通过配置文件区分

## 使用示例

### 开发环境测试
```bash
# 1. 启动开发服务
python start_dev.py

# 2. 执行查询 (使用开发环境的token)
curl -X POST http://localhost:3001/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer dev-token-123456" \
  -d '{"sql": "SELECT * FROM users", "database": "analysis_gz"}'

# 3. 获取表信息 (可以是任意数据库名称)
curl -X GET "http://localhost:3001/tables?database=any_database_name" \
  -H "Authorization: Bearer dev-token-123456"
```

### 生产环境使用
```bash
# 1. 启动生产服务
python start_prod.py

# 2. 执行查询 (使用生产环境的token，可以指定任意数据库)
curl -X POST http://localhost:3000/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer prod-secure-token-change-this-immediately" \
  -d '{"sql": "SELECT COUNT(*) FROM users", "database": "any_database_name"}'
```

## 注意事项

1. **生产环境**: 请务必修改 `.env.production` 中的API Token和数据库配置
2. **网络安全**: 生产环境建议配置防火墙和HTTPS
3. **Token管理**: API Token应该定期更换
4. **日志监控**: 建议添加访问日志和错误监控
