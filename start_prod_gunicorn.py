#!/usr/bin/env python3
"""
生产环境启动脚本 - 使用Gunicorn
"""

import os
import subprocess
import sys
import platform

# 设置环境变量
os.environ['ENVIRONMENT'] = 'production'

def main():
    print("启动生产环境 (Gunicorn)...")

    # 检查操作系统
    if platform.system() == 'Windows':
        print("⚠️  警告: Gunicorn 不支持 Windows")
        print("⚠️  建议使用以下替代方案:")
        print("   1. 使用 WSL (Windows Subsystem for Linux)")
        print("   2. 使用 Docker")
        print("   3. 部署到 Linux 服务器")
        print("   4. 开发环境使用: python start_dev.py")
        print("\n如果要强制尝试，请使用: pip install eventlet")
        print("然后运行: python sql_tools.py")
        return

    # Linux/macOS 环境
    cmd = [
        'gunicorn',
        '--bind', '0.0.0.0:3000',
        '--workers', '4',
        '--worker-class', 'sync',
        '--timeout', '120',
        '--keep-alive', '2',
        '--max-requests', '1000',
        '--max-requests-jitter', '100',
        '--preload',
        '--access-logfile', '-',
        '--error-logfile', '-',
        '--log-level', 'info',
        'sql_tools:app'
    ]

    try:
        print("Gunicorn 配置:")
        print("- 地址: 0.0.0.0:3000")
        print("- Workers: 4")
        print("- 超时: 120秒")
        print("- 环境: production")
        print("-" * 40)

        subprocess.run(cmd)
    except FileNotFoundError:
        print("错误: 未找到 gunicorn，请先安装:")
        print("pip install gunicorn")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()