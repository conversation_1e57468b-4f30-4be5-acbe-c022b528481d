#!/usr/bin/env python3
"""
生产环境启动脚本 - 使用Gunicorn
"""

import os
import subprocess
import sys

# 设置环境变量
os.environ['ENVIRONMENT'] = 'production'

if __name__ == '__main__':
    print("启动生产环境 (Gunicorn)...")
    
    # 使用Gunicorn启动
    cmd = [
        'gunicorn',
        '--bind', '0.0.0.0:3000',
        '--workers', '4',
        '--timeout', '120',
        '--keep-alive', '2',
        '--max-requests', '1000',
        '--max-requests-jitter', '100',
        '--access-logfile', '-',
        '--error-logfile', '-',
        'sql_tools:app'
    ]
    
    try:
        subprocess.run(cmd)
    except FileNotFoundError:
        print("错误: 未找到gunicorn，请先安装:")
        print("pip install gunicorn")
        sys.exit(1)
