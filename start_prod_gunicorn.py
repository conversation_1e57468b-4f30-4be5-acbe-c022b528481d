#!/usr/bin/env python3
"""
生产环境启动脚本 - 使用Waitress (Windows兼容)
"""

import os
import sys

# 设置环境变量
os.environ['ENVIRONMENT'] = 'production'

if __name__ == '__main__':
    print("启动生产环境 (Waitress)...")

    try:
        from waitress import serve
        from sql_tools import app

        print("服务器配置:")
        print("- 地址: 0.0.0.0:3000")
        print("- 线程数: 4")
        print("- 连接超时: 120秒")
        print("- 环境: production")
        print("-" * 40)

        # 使用Waitress启动
        serve(
            app,
            host='0.0.0.0',
            port=3000,
            threads=4,
            connection_limit=1000,
            cleanup_interval=30,
            channel_timeout=120
        )

    except ImportError:
        print("错误: 未找到waitress，请先安装:")
        print("pip install waitress")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)
